import { prisma } from './prisma';
import type {
  User,
  Document,
  Job,
  Template,
  A<PERSON><PERSON>ey,
  ExtractedData,
  CreditTransaction,
  SubscriptionPlan,
  DocumentStatus,
  JobStatus,
  JobType,
  CreditTransactionType,
} from '@prisma/client';

// User operations
export const userOperations = {
  async findByEmail(email: string) {
    return prisma.user.findUnique({
      where: { email },
      include: {
        creditTransactions: {
          orderBy: { createdAt: 'desc' },
          take: 10,
        },
      },
    });
  },

  async findById(userId: string) {
    return prisma.user.findUnique({
      where: { id: userId },
      include: {
        creditTransactions: {
          orderBy: { createdAt: 'desc' },
          take: 10,
        },
      },
    });
  },

  async create(data: {
    id: string; // Clerk user ID
    email: string;
    name?: string;
    imageUrl?: string;
  }) {
    return prisma.user.create({
      data,
    });
  },

  async update(
    userId: string,
    data: {
      email?: string;
      name?: string;
      imageUrl?: string;
    }
  ) {
    return prisma.user.update({
      where: { id: userId },
      data,
    });
  },

  async delete(userId: string) {
    return prisma.user.delete({
      where: { id: userId },
    });
  },

  async updateSubscription(
    userId: string,
    plan: SubscriptionPlan,
    stripeCustomerId?: string,
    stripeSubscriptionId?: string
  ) {
    // Calculate new monthly credit limit based on plan
    const creditLimits = {
      FREE: 10,
      STARTER: 500,
      PROFESSIONAL: 2000,
      ENTERPRISE: 10000,
    };

    return prisma.user.update({
      where: { id: userId },
      data: {
        subscriptionPlan: plan,
        stripeCustomerId,
        stripeSubscriptionId,
        subscriptionStatus: 'active',
        monthlyCreditsLimit: creditLimits[plan],
      },
    });
  },

  async checkCreditBalance(userId: string) {
    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: {
        creditBalance: true,
        monthlyCreditsUsed: true,
        monthlyCreditsLimit: true,
        subscriptionPlan: true,
      },
    });
    return user;
  },

  async hasCredits(userId: string, requiredCredits: number) {
    const user = await this.checkCreditBalance(userId);
    return user ? user.creditBalance >= requiredCredits : false;
  },
};

// Document operations
export const documentOperations = {
  async create(data: {
    filename: string;
    originalName: string;
    mimeType: string;
    size: number;
    storageUrl: string;
    userId: string;
  }) {
    return prisma.document.create({
      data,
    });
  },

  async updateStatus(documentId: string, status: DocumentStatus) {
    return prisma.document.update({
      where: { id: documentId },
      data: { status },
    });
  },

  async findByUser(userId: string, limit = 10, offset = 0) {
    return prisma.document.findMany({
      where: { userId },
      orderBy: { uploadedAt: 'desc' },
      take: limit,
      skip: offset,
      include: {
        jobs: {
          orderBy: { createdAt: 'desc' },
          take: 1,
        },
      },
    });
  },

  // Calculate credits required for document processing
  calculateCreditsRequired(pageCount: number): number {
    // Flat rate: 1 credit = 1 page
    return pageCount;
  },

  // Helper to determine page count from document metadata
  async getPageCount(documentId: string): Promise<number> {
    // This would typically involve calling a service to analyze the document
    // For now, return a default or stored value
    const document = await prisma.document.findUnique({
      where: { id: documentId },
      select: { mimeType: true, size: true },
    });

    if (!document) return 1;

    // Simple heuristic based on file size and type
    // In production, you'd use a proper document analysis service
    if (document.mimeType === 'application/pdf') {
      // Rough estimate: 100KB per page for PDF
      return Math.max(1, Math.ceil(document.size / 100000));
    } else if (document.mimeType.startsWith('image/')) {
      // Images are always 1 page
      return 1;
    }

    return 1; // Default to 1 page
  },
};

// Job operations
export const jobOperations = {
  async create(data: {
    userId: string;
    documentId: string;
    type: JobType;
    templateId?: string;
  }) {
    return prisma.job.create({
      data,
    });
  },

  async updateStatus(jobId: string, status: JobStatus, error?: string) {
    const updateData: any = { status };

    if (status === 'RUNNING') {
      updateData.startedAt = new Date();
    } else if (status === 'COMPLETED' || status === 'FAILED') {
      updateData.completedAt = new Date();
    }

    if (error) {
      updateData.error = error;
    }

    return prisma.job.update({
      where: { id: jobId },
      data: updateData,
    });
  },

  async updateProgress(jobId: string, progress: number) {
    return prisma.job.update({
      where: { id: jobId },
      data: { progress },
    });
  },

  async findByUser(userId: string, limit = 10, offset = 0) {
    return prisma.job.findMany({
      where: { userId },
      orderBy: { createdAt: 'desc' },
      take: limit,
      skip: offset,
      include: {
        document: true,
        template: true,
      },
    });
  },

  async findById(jobId: string) {
    return prisma.job.findUnique({
      where: { id: jobId },
      include: { document: true },
    });
  },

  async saveResult(
    jobId: string,
    result: object,
    userId: string,
    documentId: string,
    confidence?: number
  ) {
    return prisma.extractedData.create({
      data: result,
      confidence,
      userId,
      documentId,
      jobId,
    });
  },
};

// Template operations
export const templateOperations = {
  async findPublic() {
    return prisma.template.findMany({
      where: { isPublic: true },
      orderBy: { name: 'asc' },
    });
  },

  async findByUser(userId: string) {
    return prisma.template.findMany({
      where: { userId },
      orderBy: { createdAt: 'desc' },
    });
  },

  async create(data: {
    name: string;
    description?: string;
    documentType: string;
    fields: any;
    userId: string;
  }) {
    return prisma.template.create({
      data,
    });
  },
};

// API Key operations
export const apiKeyOperations = {
  async create(data: { name: string; key: string; userId: string }) {
    return prisma.apiKey.create({
      data,
    });
  },

  async findByKey(key: string) {
    return prisma.apiKey.findUnique({
      where: { key },
      include: { user: true },
    });
  },

  async findByUser(userId: string): Promise<ApiKey[]> {
    return prisma.apiKey.findMany({
      where: { userId },
      orderBy: { createdAt: 'desc' },
    });
  },

  async updateUsage(keyId: string) {
    return prisma.apiKey.update({
      where: { id: keyId },
      data: {
        requestCount: { increment: 1 },
        lastUsedAt: new Date(),
      },
    });
  },
};

// Extracted Data operations
export const extractedDataOperations = {
  async create(data: {
    data: any;
    confidence?: number;
    userId: string;
    documentId: string;
    jobId: string;
  }) {
    return prisma.extractedData.create({
      data,
    });
  },

  async validate(id: string, validatedBy: string) {
    return prisma.extractedData.update({
      where: { id },
      data: {
        isValidated: true,
        validatedAt: new Date(),
        validatedBy,
      },
    });
  },

  async findByDocument(documentId: string) {
    return prisma.extractedData.findMany({
      where: { documentId },
      orderBy: { createdAt: 'desc' },
    });
  },
};

// Credit Transaction operations
export const creditOperations = {
  async consumeCredits(
    userId: string,
    amount: number,
    description: string,
    jobId?: string,
    documentId?: string
  ) {
    return prisma.$transaction(async (tx) => {
      // Check if user has enough credits
      const user = await tx.user.findUnique({
        where: { id: userId },
        select: { creditBalance: true },
      });

      if (!user || user.creditBalance < amount) {
        throw new Error('Insufficient credits');
      }

      // Deduct credits from user balance
      await tx.user.update({
        where: { id: userId },
        data: {
          creditBalance: { decrement: amount },
          monthlyCreditsUsed: { increment: amount },
          totalCreditsUsed: { increment: amount },
        },
      });

      // Create credit transaction record
      return tx.creditTransaction.create({
        data: {
          userId,
          amount: -amount, // Negative for consumption
          type: 'CONSUMPTION',
          description,
          jobId,
          documentId,
        },
      });
    });
  },

  async addCredits(
    userId: string,
    amount: number,
    type: CreditTransactionType,
    description: string
  ) {
    return prisma.$transaction(async (tx) => {
      // Add credits to user balance
      await tx.user.update({
        where: { id: userId },
        data: {
          creditBalance: { increment: amount },
        },
      });

      // Create credit transaction record
      return tx.creditTransaction.create({
        data: {
          userId,
          amount, // Positive for addition
          type,
          description,
        },
      });
    });
  },

  async getCreditHistory(userId: string, limit = 50, offset = 0) {
    return prisma.creditTransaction.findMany({
      where: { userId },
      orderBy: { createdAt: 'desc' },
      take: limit,
      skip: offset,
      include: {
        job: {
          select: { id: true, type: true },
        },
        document: {
          select: { id: true, originalName: true },
        },
      },
    });
  },

  async resetMonthlyCredits(userId: string) {
    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: {
        monthlyCreditsLimit: true,
        creditBalance: true,
        monthlyCreditsUsed: true,
      },
    });

    if (!user) return null;

    // Calculate rollover credits (up to 2x monthly limit)
    const maxRollover = user.monthlyCreditsLimit * 2;
    const newBalance = Math.min(
      user.creditBalance + user.monthlyCreditsLimit,
      maxRollover
    );

    return prisma.$transaction(async (tx) => {
      // Reset monthly usage and update balance
      await tx.user.update({
        where: { id: userId },
        data: {
          monthlyCreditsUsed: 0,
          creditBalance: newBalance,
          lastCreditReset: new Date(),
        },
      });

      // Record the monthly credit allocation
      return tx.creditTransaction.create({
        data: {
          userId,
          amount: user.monthlyCreditsLimit,
          type: 'SUBSCRIPTION',
          description: 'Monthly credit allocation',
        },
      });
    });
  },
};

// Export types for use in other files
export type {
  User,
  Document,
  Job,
  Template,
  ApiKey,
  ExtractedData,
  CreditTransaction,
  SubscriptionPlan,
  DocumentStatus,
  JobStatus,
  JobType,
  CreditTransactionType,
};

// Helper functions for process route
export async function getJobById(jobId: string) {
  return jobOperations.findById(jobId);
}
export async function updateJobStatus(
  jobId: string,
  status: JobStatus,
  error?: string
) {
  return jobOperations.updateStatus(jobId, status, error);
}
export async function saveJobResult(jobId: string, result: any) {
  return jobOperations.saveResult(jobId, result);
}
export async function getUserById(userId: string) {
  return userOperations.findById(userId);
}
export async function decrementUserCredits(userId: string, amount: number) {
  return creditOperations.consumeCredits(userId, amount, 'Document processing');
}
