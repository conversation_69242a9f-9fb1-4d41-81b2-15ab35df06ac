// This module provides a switchable interface for AI document processing.
// To add a new AI model, implement a new function and update the switch below.

import { callGoogleVisionAPI } from './providers/google-vision';
import { callOpenAIVisionAPI } from './providers/openai-vision';
import {
  callMistralOCRAPI,
  type MistralOCROptions,
} from './providers/mistral-ocr';
// import { callOtherVisionAPI } from './providers/other-vision';

export type AIResult = {
  pages: number;
  data: any;
  confidence?: number;
};

export async function processDocumentWithAI(
  documentUrl: string,
  model: 'google' | 'openai' | 'mistral' | 'other' = 'google',
  options?: MistralOCROptions
): Promise<AIResult> {
  switch (model) {
    case 'google':
      return callGoogleVisionAPI(documentUrl);
    case 'openai':
      return callOpenAIVisionAPI(documentUrl);
    case 'mistral':
      return callMistralOCRAPI(documentUrl, options);
    // case 'other':
    //   return callOtherVisionAPI(documentUrl);
    default:
      throw new Error('Unsupported AI model');
  }
}
